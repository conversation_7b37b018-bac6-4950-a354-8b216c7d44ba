<?php

namespace App\Filament\Resources;

use App\Enums\AgentModelType;
use App\Enums\AgentVerbosity;
use App\Enums\CollectionType;
use App\Enums\AgentReasoningEffort;
use App\Enums\SourceType;
use App\Features\AgentMedia;
use App\Filament\RelationManagers\HistoryRelationManager;
use App\Filament\Resources\AgentResource\Pages\EditFrontend;
use App\Filament\Resources\AgentResource\Pages\EditRecord;
use App\Filament\Resources\AgentResource\Pages\ShowHistory;
use App\Filament\Resources\AgentResource\RelationManagers\AgentCtasRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentIntegrationsRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentQuestionsRelationManager;
use App\Filament\Resources\AgentResource\RelationManagers\AgentTokensRelationManager;
use App\Filament\Resources\Resource as BaseResource;
use App\Filament\Traits\CanBeAdministered;
use App\Filament\Traits\CanBeLocked;
use App\Filament\Traits\CanBeModerated;
use App\Filament\Traits\CanBeOrdered;
use App\Filament\Traits\CanBePromoted;
use App\Filament\Traits\CanBeReplicated;
use App\Models\Agent;
use App\Models\AgentModel;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Contributor;
use App\Models\Source;
use App\Models\Tag;
use App\Models\Team;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Awcodes\Shout\Components\Shout;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Closure;
use dacoto\DomainValidator\Validator\Domain;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieTagsInput;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Pages\SubNavigationPosition;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Pages\Page;
use Filament\Resources\Pages\PageRegistration;
use Filament\Support\Enums\Alignment;
use Filament\Support\Enums\IconPosition;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Laravel\Pennant\Feature;
use RalphJSmit\Filament\RecordFinder\Forms\Components\RecordFinder;

class AgentResource extends BaseResource implements HasShieldPermissions
{
    use CanBeAdministered;
    use CanBeLocked;
    use CanBeModerated;
    use CanBeOrdered;
    use CanBePromoted;
    use CanBeReplicated;
    use Translatable;

    protected static ?string $model = Agent::class;

    protected static ?string $recordTitleAttribute = 'name';

    protected static ?string $navigationIcon = 'heroicon-s-sparkles';

    protected static ?string $pluralModelLabel = 'Agents';

    protected static string $createLabel = 'create';

    protected static array $customPermissions = [
        'test',
    ];

    protected const FINDER_PAGE_SIZE = 10;

    protected static SubNavigationPosition $subNavigationPosition = SubNavigationPosition::Top;

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return static::decorateOrderableTable($table
            ->modifyQueryUsing(function (Builder $query) {
                static::$isScopedToTenant = false;
                if (! Gatekeeper::userCanAdminister(static::getModel())) {
                    $query->whereBelongsTo(get_current_team());
                }
            })
            ->columns([
                static::getTableColumnName(),
                TextColumn::make('target_worldview')
                    ->label('Target Worldview'),
                static::getTableColumnActive(),
                static::getTableColumnCreated(),
                static::getTableColumnUpdated(),
            ])
            ->filters([
                static::getTableFilterBoolean('is_active', 'Active'),
                static::getTableFilterBoolean('is_locked', 'Locked', 'Unlocked'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
                static::getTableFilterArchived(),
                static::getTableFilterTeam(),
            ])
            ->actions([
                static::decorateLockAction(\Filament\Tables\Actions\Action::make('Lock')),
                static::decorateUnlockAction(\Filament\Actions\Action::make('Unlock')),
                static::decorateViewAction(
                    Action::make('View'),
                    fn ($record) => $record->is_active ? 'View Agent' : 'Agent Not Active',
                    fn ($record) => $record->is_active,
                    fn ($record) => $record->getUrl()
                ),
                static::getTableActionEditRow(),
            ])
            ->bulkActions(static::getTableBulkActions())
            ->paginated(static::$pageOptions)
            ->defaultPaginationPageOption(static::$defaultPageOption)
        );
    }


    /**
     * @return string[]
     */
    public static function getRelations(): array
    {
        return [
            AgentQuestionsRelationManager::class,
            AgentCtasRelationManager::class,
            AgentIntegrationsRelationManager::class,
            AgentTokensRelationManager::class,
            HistoryRelationManager::class,
        ];
    }

    /**
     * @return array|PageRegistration[]
     *
     * @throws Exception
     */
    public static function getPages(): array
    {
        return array_merge(
            parent::getPages(),
            ['edit-frontend' => EditFrontend::route('/{record}/edit-frontend')]
        );
    }

    public static function getRecordSubNavigation(Page $page): array
    {
        return $page->generateNavigationItems([
            EditRecord::class,
            EditFrontend::class,
        ]);
    }

}
